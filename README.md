---
title: محول الفيديو/الصوت إلى نص مع API | Video/Audio to Text Converter with API
emoji: 🎤
colorFrom: blue
colorTo: purple
sdk: gradio
sdk_version: 4.44.0
app_file: app_with_api.py
pinned: false
license: mit
---

# 🎤 محول الفيديو/الصوت إلى نص مع API
# Video/Audio to Text Converter with API

تطبيق متقدم لتحويل الملفات الصوتية والفيديو إلى نص باستخدام نموذج Whisper من OpenAI مع API Key تلقائي.

Advanced application for converting audio and video files to text using OpenAI's Whisper model with automatic API Key generation.

## ✨ المميزات الجديدة | New Features

- 🔑 **API Key تلقائي**: يتم إنشاء API Key فريد عند كل تشغيل
- 🚀 **تشغيل تلقائي**: نموذج Small يتم تحميله تلقائياً
- 📦 **نماذج محلية**: النماذج محفوظة محلياً للسرعة
- 🔒 **أمان محسن**: التحقق من API Key للاستخدام البرمجي
- 📊 **معلومات مفصلة**: إحصائيات وتفاصيل التحويل

## 🎯 النماذج المتاحة | Available Models

| النموذج | الحجم | الدقة | السرعة | الحالة |
|---------|-------|-------|--------|--------|
| tiny    | 72MB  | جيد   | سريع جداً | ✅ محلي |
| base    | 139MB | جيد جداً | سريع | ✅ محلي |
| small   | 461MB | ممتاز | متوسط | ✅ محلي |

## 🔑 استخدام API

### الحصول على API Key
بمجرد تشغيل التطبيق، ستجد API Key الخاص بك في تبويب "API Key & Usage"

### مثال على الاستخدام
```python
import requests
import json

API_URL = "https://YOUR_USERNAME-YOUR_SPACE_NAME.hf.space/api/predict"
API_KEY = "YOUR_GENERATED_API_KEY"

# رفع ملف وتحويله
with open("audio.mp3", "rb") as f:
    files = {"data": f}
    data = {
        "data": [
            None,  # audio_file
            "small",  # model
            "Arabic",  # language
            "transcribe",  # task
            API_KEY  # api_key
        ]
    }
    
response = requests.post(API_URL, files=files, data=json.dumps(data))
result = response.json()
print(result["data"][0])  # النص المستخرج
```

## 🚀 التشغيل التلقائي

عند رفع التطبيق على Hugging Face:

1. ✅ **تحميل تلقائي**: نموذج Small يتم تحميله تلقائياً
2. ✅ **API Key فريد**: يتم إنشاء مفتاح API جديد
3. ✅ **جاهز للاستخدام**: التطبيق يعمل فوراً
4. ✅ **واجهة سهلة**: تصميم بسيط ومتجاوب

## 📁 الملفات المطلوبة للرفع | Required Files for Upload

```
📦 ارفع هذه الملفات على Hugging Face:
├── 📄 README.md (هذا الملف)
├── 📄 app_with_api.py (التطبيق الرئيسي)
├── 📄 requirements.txt (المكتبات)
├── 📄 startup.py (بدء التشغيل)
└── 📁 models/ (مجلد النماذج)
    ├── 📄 tiny.pt (72MB)
    ├── 📄 base.pt (139MB)
    └── 📄 small.pt (461MB)
```

## 🌍 اللغات المدعومة | Supported Languages

- العربية (Arabic) ✅
- الإنجليزية (English) ✅  
- الفرنسية (French) ✅
- الإسبانية (Spanish) ✅
- الألمانية (German) ✅
- وأكثر من 95 لغة أخرى

## 📊 الأداء | Performance

- **الذاكرة**: 2-6 GB حسب النموذج
- **السرعة**: 1-3 دقائق لكل ساعة صوت  
- **الدقة**: 85-95% حسب جودة الصوت
- **التوافق**: جميع صيغ الصوت والفيديو الشائعة

## 🔒 الأمان | Security

- 🔑 API Key فريد لكل جلسة
- 🚫 عدم حفظ الملفات على الخادم
- ✅ التحقق من صحة المدخلات
- 🔐 تشفير الاتصالات

## 📱 الاستخدام | Usage

### عبر الواجهة
1. ارفع ملف صوتي أو فيديو
2. اختر النموذج والإعدادات
3. اضغط "تحويل"
4. احصل على النص

### عبر API
1. احصل على API Key من التطبيق
2. استخدم الكود المرفق
3. أرسل الملف مع المعاملات
4. استقبل النتيجة

## 🛠️ التطوير | Development

```bash
# تشغيل محلي
python app_with_api.py

# تشغيل startup
python startup.py
```

## 📄 الترخيص | License

هذا المشروع مرخص تحت رخصة MIT - مجاني للاستخدام التجاري والشخصي.

---

**🎉 جاهز للاستخدام فوراً! | Ready to use immediately!**
