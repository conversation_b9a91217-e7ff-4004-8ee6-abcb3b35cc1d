import gradio as gr
import whisper
import os

# API Key بسيط للاستخدام الشخصي
API_KEY = "whisper-personal-2025"

# متغير النموذج
model = None

def load_model():
    """تحميل النموذج"""
    global model
    try:
        # محاولة تحميل النماذج المحلية أولاً
        if os.path.exists("./models/tiny.pt"):
            model = whisper.load_model("./models/tiny.pt")
            return "✅ تم تحميل نموذج tiny محلي"
        elif os.path.exists("./models/base.pt"):
            model = whisper.load_model("./models/base.pt")
            return "✅ تم تحميل نموذج base محلي"
        elif os.path.exists("./models/small.pt"):
            model = whisper.load_model("./models/small.pt")
            return "✅ تم تحميل نموذج small محلي"
        else:
            # تحميل من الإنترنت
            model = whisper.load_model("tiny")
            return "✅ تم تحميل نموذج tiny من الإنترنت"
    except Exception as e:
        return f"❌ خطأ: {str(e)}"

def transcribe(audio):
    """تحويل الصوت إلى نص"""
    if audio is None:
        return "❌ يرجى رفع ملف صوتي"

    if model is None:
        return "❌ النموذج غير محمل"

    try:
        result = model.transcribe(audio)
        return result["text"]
    except Exception as e:
        return f"❌ خطأ: {str(e)}"

# تحميل النموذج
print("🚀 تحميل النموذج...")
status = load_model()
print(status)

# الواجهة البسيطة
with gr.Blocks(title="محول شخصي للصوت") as app:

    gr.HTML("""
    <div style="text-align: center; padding: 15px; background: #e3f2fd; border-radius: 8px;">
        <h2>🎤 محول الصوت إلى نص - للاستخدام الشخصي</h2>
    </div>
    """)

    # عرض API Key
    gr.HTML(f"""
    <div style="background: #f1f8e9; padding: 10px; border-radius: 5px; margin: 10px 0;">
        <strong>🔑 API Key الخاص بك:</strong>
        <code style="background: white; padding: 3px 6px; border-radius: 3px;">{API_KEY}</code>
    </div>
    """)

    # واجهة التحويل
    with gr.Row():
        audio_input = gr.Audio(
            label="📁 ارفع ملف صوتي",
            type="filepath"
        )
        output_text = gr.Textbox(
            label="📝 النص",
            lines=8
        )

    convert_btn = gr.Button("🚀 تحويل", variant="primary")

    # معلومات API
    gr.HTML(f"""
    <div style="background: #fafafa; padding: 15px; border-radius: 5px; margin: 10px 0;">
        <h4>📡 استخدام API:</h4>
        <pre style="background: #333; color: #fff; padding: 10px; border-radius: 3px; font-size: 12px;">
import requests
import json

API_URL = "https://YOUR-SPACE-URL.hf.space/api/predict"

with open("audio.mp3", "rb") as f:
    files = {{"data": f}}
    data = {{"data": [None]}}

response = requests.post(API_URL, files=files, data=json.dumps(data))
result = response.json()
print(result["data"][0])
        </pre>
        <p><strong>API Key:</strong> {API_KEY}</p>
    </div>
    """)

    # ربط الحدث
    convert_btn.click(
        fn=transcribe,
        inputs=audio_input,
        outputs=output_text
    )

if __name__ == "__main__":
    print("✅ التطبيق جاهز للاستخدام الشخصي")
    app.launch(share=True, server_name="0.0.0.0", server_port=7860)
