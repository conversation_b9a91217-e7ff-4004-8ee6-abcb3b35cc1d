import gradio as gr
import whisper
import os
import time

# إنشاء API Key بسيط
API_KEY = f"whisper-{int(time.time())}"

# متغير النموذج
model = None
model_status = "غير محمل"

def load_whisper_model():
    """تحميل نموذج Whisper"""
    global model, model_status
    try:
        # محاولة تحميل النماذج المحلية
        if os.path.exists("./models/tiny.pt"):
            model = whisper.load_model("./models/tiny.pt")
            model_status = "tiny (محلي)"
        elif os.path.exists("./models/base.pt"):
            model = whisper.load_model("./models/base.pt")
            model_status = "base (محلي)"
        elif os.path.exists("./models/small.pt"):
            model = whisper.load_model("./models/small.pt")
            model_status = "small (محلي)"
        else:
            # تحميل من الإنترنت
            model = whisper.load_model("tiny")
            model_status = "tiny (إنترنت)"
        return f"✅ تم تحميل النموذج: {model_status}"
    except Exception as e:
        model_status = f"خطأ: {str(e)}"
        return f"❌ خطأ: {str(e)}"

def transcribe_audio(audio_file):
    """تحويل الصوت إلى نص"""
    if audio_file is None:
        return "❌ يرجى رفع ملف صوتي"

    if model is None:
        return "❌ النموذج غير محمل"

    try:
        result = model.transcribe(audio_file)
        return result["text"]
    except Exception as e:
        return f"❌ خطأ في التحويل: {str(e)}"

# تحميل النموذج عند البدء
print("🚀 بدء التطبيق...")
load_status = load_whisper_model()
print(load_status)
print(f"🔑 API Key: {API_KEY}")

# إنشاء الواجهة
def create_interface():
    with gr.Blocks(title="محول الصوت إلى نص") as demo:

        # العنوان
        gr.HTML("""
        <div style="text-align: center; padding: 20px; background: linear-gradient(90deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 10px; margin: 10px;">
            <h1>🎤 محول الصوت إلى نص</h1>
            <h2>Audio to Text Converter</h2>
        </div>
        """)

        # معلومات API والنموذج
        with gr.Row():
            with gr.Column():
                gr.HTML(f"""
                <div style="background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px; border: 1px solid #c3e6cb;">
                    <h3 style="color: #155724; margin: 0;">🔑 API Key:</h3>
                    <p style="font-family: monospace; background: white; padding: 8px; border-radius: 4px; margin: 5px 0; word-break: break-all;">
                        {API_KEY}
                    </p>
                </div>
                """)

            with gr.Column():
                gr.HTML(f"""
                <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px; border: 1px solid #ffeaa7;">
                    <h3 style="color: #856404; margin: 0;">🤖 النموذج:</h3>
                    <p style="font-family: monospace; background: white; padding: 8px; border-radius: 4px; margin: 5px 0;">
                        {model_status}
                    </p>
                </div>
                """)

        # واجهة التحويل
        with gr.Row():
            with gr.Column():
                audio_input = gr.Audio(
                    label="📁 ارفع ملف صوتي أو فيديو",
                    type="filepath"
                )
                convert_btn = gr.Button(
                    "🚀 تحويل إلى نص",
                    variant="primary",
                    size="lg"
                )

            with gr.Column():
                output_text = gr.Textbox(
                    label="📝 النص المستخرج",
                    lines=10,
                    placeholder="النص سيظهر هنا بعد التحويل..."
                )

        # معلومات الاستخدام
        gr.HTML(f"""
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; border: 1px solid #dee2e6;">
            <h3>📡 كيفية استخدام API:</h3>
            <pre style="background: #343a40; color: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; font-size: 12px;">
import requests
import json

# استبدل YOUR_SPACE_URL برابط Space الخاص بك
API_URL = "https://YOUR_USERNAME-YOUR_SPACE_NAME.hf.space/api/predict"

# رفع ملف وتحويله
with open("audio.mp3", "rb") as f:
    files = {{"data": f}}
    data = {{"data": [None]}}

response = requests.post(API_URL, files=files, data=json.dumps(data))
result = response.json()
print("النص:", result["data"][0])
            </pre>

            <div style="margin-top: 15px;">
                <h4>📋 معلومات مهمة:</h4>
                <ul>
                    <li><strong>API Key:</strong> {API_KEY}</li>
                    <li><strong>النموذج:</strong> {model_status}</li>
                    <li><strong>الصيغ المدعومة:</strong> MP3, WAV, MP4, AVI, MOV</li>
                    <li><strong>اللغات:</strong> العربية، الإنجليزية، وأكثر من 90 لغة</li>
                </ul>
            </div>
        </div>
        """)

        # ربط الأحداث
        convert_btn.click(
            fn=transcribe_audio,
            inputs=[audio_input],
            outputs=[output_text]
        )

    return demo

# إنشاء وتشغيل التطبيق
app = create_interface()

if __name__ == "__main__":
    print("✅ التطبيق جاهز!")
    app.launch(server_name="0.0.0.0", server_port=7860, share=True)
