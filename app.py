import gradio as gr
import whisper
import os
import time

# إنشاء API Key بسيط
API_KEY = f"whisper-api-{int(time.time())}"

# متغير النموذج
current_model = None
model_name = "غير محمل"

def load_model():
    """تحميل النموذج"""
    global current_model, model_name
    try:
        # محاولة تحميل النماذج المحلية
        if os.path.exists("./models/tiny.pt"):
            current_model = whisper.load_model("./models/tiny.pt")
            model_name = "tiny (محلي)"
        elif os.path.exists("./models/base.pt"):
            current_model = whisper.load_model("./models/base.pt")
            model_name = "base (محلي)"
        elif os.path.exists("./models/small.pt"):
            current_model = whisper.load_model("./models/small.pt")
            model_name = "small (محلي)"
        else:
            # تحميل من الإنترنت
            current_model = whisper.load_model("tiny")
            model_name = "tiny (إنترنت)"
        return f"✅ تم تحميل النموذج: {model_name}"
    except Exception as e:
        return f"❌ خطأ: {str(e)}"

def transcribe(audio_file):
    """تحويل الصوت إلى نص"""
    if audio_file is None:
        return "❌ يرجى رفع ملف صوتي"
    
    if current_model is None:
        return "❌ النموذج غير محمل"
    
    try:
        result = current_model.transcribe(audio_file)
        return result["text"]
    except Exception as e:
        return f"❌ خطأ في التحويل: {str(e)}"

# تحميل النموذج عند البدء
print("🚀 بدء التطبيق...")
load_status = load_model()
print(load_status)

# الواجهة
with gr.Blocks(title="محول الصوت إلى نص") as app:
    
    gr.HTML("""
    <div style="text-align: center; padding: 20px; background: #f0f8ff; border-radius: 10px; margin: 10px;">
        <h1>🎤 محول الصوت إلى نص</h1>
        <h2>Audio to Text Converter</h2>
    </div>
    """)
    
    # عرض معلومات API والنموذج
    with gr.Row():
        with gr.Column():
            gr.HTML(f"""
            <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 10px;">
                <h3>🔑 API Key الخاص بك:</h3>
                <code style="background: white; padding: 5px; border-radius: 3px; font-size: 14px;">
                    {API_KEY}
                </code>
            </div>
            """)
        
        with gr.Column():
            gr.HTML(f"""
            <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px;">
                <h3>🤖 النموذج المحمل:</h3>
                <code style="background: white; padding: 5px; border-radius: 3px; font-size: 14px;">
                    {model_name}
                </code>
            </div>
            """)
    
    # واجهة التحويل
    with gr.Row():
        with gr.Column():
            audio_input = gr.Audio(
                label="📁 ارفع ملف صوتي أو فيديو",
                type="filepath"
            )
            convert_btn = gr.Button("🚀 تحويل إلى نص", variant="primary", size="lg")
        
        with gr.Column():
            output_text = gr.Textbox(
                label="📝 النص المستخرج",
                lines=10,
                placeholder="النص سيظهر هنا..."
            )
    
    # معلومات API
    gr.HTML(f"""
    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
        <h3>📡 استخدام API:</h3>
        <pre style="background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 5px; overflow-x: auto;">
import requests
import json

API_URL = "https://YOUR_USERNAME-YOUR_SPACE_NAME.hf.space/api/predict"
API_KEY = "{API_KEY}"

# تحويل ملف صوتي
with open("audio.mp3", "rb") as f:
    files = {{"data": f}}
    data = {{"data": [None]}}
    
response = requests.post(API_URL, files=files, data=json.dumps(data))
result = response.json()
print("النص:", result["data"][0])
        </pre>
        
        <h4>🌐 رابط Space:</h4>
        <p>استبدل YOUR_USERNAME-YOUR_SPACE_NAME برابط Space الخاص بك</p>
        
        <h4>🔧 معلومات النموذج:</h4>
        <ul>
            <li><strong>النموذج الحالي:</strong> {model_name}</li>
            <li><strong>API Key:</strong> {API_KEY}</li>
            <li><strong>الحالة:</strong> ✅ جاهز للاستخدام</li>
        </ul>
    </div>
    """)
    
    # ربط الأحداث
    convert_btn.click(
        fn=transcribe,
        inputs=[audio_input],
        outputs=[output_text]
    )

# تشغيل التطبيق
if __name__ == "__main__":
    print(f"🔑 API Key: {API_KEY}")
    print(f"🤖 النموذج: {model_name}")
    print("✅ التطبيق جاهز!")
    app.launch()
